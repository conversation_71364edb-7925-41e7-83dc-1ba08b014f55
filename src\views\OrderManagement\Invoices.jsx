import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Row,
  Col,
  Breadcrumb,
  InputGroup,
  Form,
  Pagination
} from 'react-bootstrap';
import {
  FaFileInvoice,
  FaEye,
  FaPrint,
  FaDownload,
  FaHome,
  FaSearch,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaUser,
  FaCreditCard,
  FaServer,
  FaTimes,
  FaShoppingCart
} from 'react-icons/fa';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

/**
 * Invoices Component
 *
 * This component fetches invoice data using the /api/paiements endpoint to get the list of invoices,
 * but for the payment method (Méthode de paiement) field specifically, it uses ONLY the
 * 'methode_paiement' field directly from the /api/commandes response (not from paiement relationship).
 */
const Invoices = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Modal states
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch payments which represent our invoices
      const paymentsResponse = await fetch(`${API_URL}/paiements`);
      if (!paymentsResponse.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const paymentsData = await paymentsResponse.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];

      console.log('💳 Payments API response:', paymentsData);
      console.log('💳 Processed payments:', payments);

      // For each payment, fetch the related order to get complete invoice data including payment method from commande API
      const invoicesWithOrders = await Promise.all(
        payments.map(async (payment) => {
          try {
            // Fetch order data to get methode_paiement field directly from commande API
            console.log(
              `🔗 Fetching order ${payment.commande_id} from: ${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`
            );
            const orderResponse = await fetch(`${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`);

            console.log(`📡 Order response status for ${payment.commande_id}:`, orderResponse.status, orderResponse.ok);

            if (orderResponse.ok) {
              const orderResponseData = await orderResponse.json();
              console.log(`📦 Raw order response for ${payment.commande_id}:`, orderResponseData);

              // Extract the actual order data from the response
              const orderData = orderResponseData.data || orderResponseData;
              console.log(`📋 Extracted order data for ${payment.commande_id}:`, orderData);

              // Debug logging to see what payment method we're getting
              console.log('🔍 Payment method debug for order', payment.commande_id, ':', {
                'orderData.methode_paiement': orderData.methode_paiement
              });

              // Use ONLY methode_paiement field directly from API commande
              const finalPaymentMethod = orderData.methode_paiement || null;

              console.log('🎯 Final payment method selected:', finalPaymentMethod);

              const invoiceObject = {
                ...payment,
                order: orderData,
                invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
                invoice_date: payment.created_at,
                amount: payment.montant,
                status: payment.status || 'completed',
                // Use payment method from commande API (put AFTER spread to override)
                methode_paiement: finalPaymentMethod
              };

              console.log(`✅ Final invoice object for ${payment.commande_id}:`, {
                'invoice.methode_paiement': invoiceObject.methode_paiement,
                'payment spread keys': Object.keys(payment),
                'payment.methode_paiement': payment.methode_paiement,
                'full invoice': invoiceObject
              });

              return invoiceObject;
            }
            console.log('⚠️ Order fetch failed, keeping payment method as null for payment:', payment.id);

            return {
              ...payment,
              order: null,
              // Don't use payment data fallback - keep null if order fetch fails
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          } catch (err) {
            console.error(`❌ Error fetching order ${payment.commande_id}:`, err);

            return {
              ...payment,
              order: null,
              // Don't use payment data fallback - keep null if error occurs
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          }
        })
      );

      setInvoices(invoicesWithOrders);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Erreur lors du chargement des factures: ' + err.message);
      setInvoices([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search term
  const filteredInvoices = invoices.filter(
    (invoice) =>
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.prenom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.email_commande?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentInvoices = filteredInvoices.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle view invoice details
  const handleViewInvoice = async (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Handle print invoice
  const handlePrintInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
    // Wait for modal to render then print
    setTimeout(() => {
      window.print();
    }, 300);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency - Using TND (Tunisian Dinar)
  const formatCurrency = (amount) => {
    if (!amount) return '0,000 TND';
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Get client name with improved fallback logic
  const getClientName = (invoice) => {
    const order = invoice.order;
    if (!order) return 'Client inconnu';

    // Try different sources for client name
    if (order.user?.name) return order.user.name;
    if (order.client?.nom) return order.client.nom;
    if (order.client?.name) return order.client.name;
    if (order.nom_client) return order.nom_client;
    if (order.prenom_client) return order.prenom_client;

    // Try to construct name from first and last name
    if (order.prenom_client && order.nom_client) {
      return `${order.prenom_client} ${order.nom_client}`;
    }

    // Fallback to email username if available
    if (order.email_commande) {
      return order.email_commande.split('@')[0];
    }
    if (order.user?.email) {
      return order.user.email.split('@')[0];
    }
    if (order.client?.email) {
      return order.client.email.split('@')[0];
    }

    return 'Client inconnu';
  };

  // Get status badge variant
  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échec';
      default:
        return 'Inconnue';
    }
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaFileInvoice className="me-2" />
          Gestion des Factures
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item active>Factures</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}

      {/* Search */}
      <Card className="mb-4 shadow-sm border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Rechercher une facture (numéro, client, email, transaction)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6} className="text-end">
              <div className="d-flex align-items-center justify-content-end gap-3">
                <small className="text-muted">
                  {filteredInvoices.length > 0 && (
                    <>
                      Affichage {startIndex + 1}-{Math.min(endIndex, filteredInvoices.length)} sur {filteredInvoices.length} factures
                    </>
                  )}
                </small>
                <Button variant="outline-primary" onClick={loadInvoices} disabled={loading} className="px-4">
                  {loading ? (
                    <>
                      <Spinner size="sm" animation="border" className="me-2" />
                      Actualisation...
                    </>
                  ) : (
                    'Actualiser'
                  )}
                </Button>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Invoices Table */}
      <Card className="shadow-sm border-0">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2 text-muted">Chargement des factures...</p>
            </div>
          ) : filteredInvoices.length === 0 ? (
            <div className="text-center py-5">
              <FaFileInvoice size={48} className="text-muted mb-3" />
              <h5 className="text-muted">Aucune facture trouvée</h5>
              <p className="text-muted">
                {searchTerm ? 'Aucune facture ne correspond à votre recherche.' : 'Aucune facture disponible pour le moment.'}
              </p>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table hover className="align-middle">
                  <thead className="table-light">
                    <tr>
                      <th>N° Facture</th>
                      <th>Date</th>
                      <th>Client</th>
                      <th>Commande</th>
                      <th>Montant</th>
                      <th>Méthode</th>
                      <th>Statut</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentInvoices.map((invoice) => (
                      <tr key={invoice.id}>
                        <td>
                          <div className="fw-medium text-primary">{invoice.invoice_number}</div>
                          {invoice.transaction_id && <small className="text-muted">ID: {invoice.transaction_id}</small>}
                        </td>
                        <td>
                          <div className="small">
                            <FaCalendarAlt className="me-1" />
                            {formatDate(invoice.invoice_date)}
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="fw-medium">{getClientName(invoice)}</div>
                            {(invoice.order?.email_commande || invoice.order?.user?.email || invoice.order?.client?.email) && (
                              <small className="text-muted">
                                {invoice.order.email_commande || invoice.order.user?.email || invoice.order.client?.email}
                              </small>
                            )}
                          </div>
                        </td>
                        <td>
                          <Badge bg="info" className="rounded-pill">
                            CMD-{invoice.commande_id}
                          </Badge>
                        </td>
                        <td>
                          <div className="fw-medium text-success">
                            <FaMoneyBillWave className="me-1" />
                            {formatCurrency(invoice.amount)}
                          </div>
                        </td>
                        <td>
                          <Badge bg="secondary" className="text-capitalize">
                            {/* Use ONLY methode_paiement field directly from commande API */}
                            {invoice.methode_paiement ? invoice.methode_paiement.replace('_', ' ') : 'Non spécifiée'}
                          </Badge>
                        </td>
                        <td>
                          <Badge bg={getStatusBadge(invoice.status)}>{getStatusText(invoice.status)}</Badge>
                        </td>
                        <td className="text-center">
                          <div className="btn-group" role="group">
                            <Button size="sm" variant="outline-info" onClick={() => handleViewInvoice(invoice)} title="Voir la facture">
                              <FaEye />
                            </Button>
                            <Button size="sm" variant="outline-primary" onClick={() => handlePrintInvoice(invoice)} title="Imprimer">
                              <FaPrint />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="text-muted small">
                    Page {currentPage} sur {totalPages}
                  </div>
                  <Pagination className="mb-0">
                    <Pagination.First onClick={() => handlePageChange(1)} disabled={currentPage === 1} />
                    <Pagination.Prev onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} />

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber;
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }

                      return (
                        <Pagination.Item key={pageNumber} active={pageNumber === currentPage} onClick={() => handlePageChange(pageNumber)}>
                          {pageNumber}
                        </Pagination.Item>
                      );
                    })}

                    <Pagination.Next onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} />
                    <Pagination.Last onClick={() => handlePageChange(totalPages)} disabled={currentPage === totalPages} />
                  </Pagination>
                </div>
              )}
            </>
          )}
        </Card.Body>
      </Card>

      {/* Invoice Details Modal */}
      <Modal show={showInvoiceModal} onHide={() => setShowInvoiceModal(false)} size="lg" centered>
        <Modal.Header closeButton className="bg-primary text-white">
          <Modal.Title className="d-flex align-items-center">
            <FaFileInvoice className="me-2" />
            Facture {selectedInvoice?.invoice_number}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {selectedInvoice && (
            <div className="invoice-details p-4">
              {/* Company Header */}
              <div className="company-header mb-4">
                <Row>
                  <Col md={8}>
                    <h3 className="text-primary mb-1">jiheneLine</h3>
                    <div className="text-muted small">
                      <div>Plateforme E-commerce</div>
                      <div>Email: <EMAIL></div>
                      <div>Téléphone: +216 XX XXX XXX</div>
                    </div>
                  </Col>
                  <Col md={4} className="text-end">
                    <h4 className="text-primary mb-1">FACTURE</h4>
                    <div className="fw-bold">{selectedInvoice.invoice_number}</div>
                    <div className="small text-muted">Date: {formatDate(selectedInvoice.invoice_date)}</div>
                  </Col>
                </Row>
              </div>

              {/* Invoice Header */}
              <div className="invoice-header mb-3 p-3 bg-light rounded">
                <Row className="align-items-center">
                  <Col md={8}>
                    <h6 className="text-primary mb-2 d-flex align-items-center">
                      <FaFileInvoice className="me-2" />
                      Détails de la Facture
                    </h6>
                    <div className="d-flex flex-wrap gap-3 small text-muted">
                      <span>
                        <FaCalendarAlt className="me-1" />
                        Date: {formatDate(selectedInvoice.invoice_date)}
                      </span>
                      <span>
                        <FaShoppingCart className="me-1" />
                        Commande: CMD-{selectedInvoice.commande_id}
                      </span>
                      <span>
                        <FaMoneyBillWave className="me-1" />
                        Montant: {formatCurrency(selectedInvoice.amount)}
                      </span>
                    </div>
                  </Col>
                  <Col md={4} className="text-end">
                    <Badge bg={getStatusBadge(selectedInvoice.status)} className="fs-6 px-3 py-2">
                      {getStatusText(selectedInvoice.status)}
                    </Badge>
                  </Col>
                </Row>
              </div>

              {/* Customer Information */}
              <div className="customer-info mb-3">
                <h6 className="text-primary mb-2 d-flex align-items-center">
                  <FaUser className="me-2" />
                  Informations Client
                </h6>
                <div className="bg-light p-3 rounded">
                  <Row>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong className="text-muted small">Client:</strong>
                        <div>{getClientName(selectedInvoice)}</div>
                      </div>
                      <div className="mb-2">
                        <strong className="text-muted small">Email:</strong>
                        <div className="small">
                          {selectedInvoice.order?.user?.email ||
                            selectedInvoice.order?.email_commande ||
                            selectedInvoice.order?.client?.email ||
                            'Non spécifié'}
                        </div>
                      </div>
                      {selectedInvoice.order?.telephone_commande && (
                        <div className="mb-2">
                          <strong className="text-muted small">Téléphone:</strong>
                          <div className="small">{selectedInvoice.order.telephone_commande}</div>
                        </div>
                      )}
                    </Col>
                    <Col md={6}>
                      {selectedInvoice.order?.adresse_commande && (
                        <div className="mb-2">
                          <strong className="text-muted small">Adresse:</strong>
                          <div className="small">
                            {selectedInvoice.order.adresse_commande}
                            {selectedInvoice.order?.ville_commande && (
                              <>
                                <br />
                                {selectedInvoice.order.ville_commande}
                              </>
                            )}
                            {selectedInvoice.order?.code_postal_commande && <> {selectedInvoice.order.code_postal_commande}</>}
                          </div>
                        </div>
                      )}
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Products Section */}
              {selectedInvoice.order?.produits && selectedInvoice.order.produits.length > 0 && (
                <div className="products-info mb-3">
                  <h6 className="text-primary mb-2 d-flex align-items-center">
                    <FaShoppingCart className="me-2" />
                    Produits Commandés
                  </h6>
                  <div className="table-responsive">
                    <Table className="mb-0" size="sm">
                      <thead className="table-light">
                        <tr>
                          <th>Produit</th>
                          <th className="text-center">Quantité</th>
                          <th className="text-end">Prix Unitaire</th>
                          <th className="text-end">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedInvoice.order.produits.map((produit, index) => (
                          <tr key={produit.id || index}>
                            <td>
                              <div className="fw-medium">{produit.nom || produit.nom_produit || 'Produit'}</div>
                              {produit.description && <small className="text-muted">{produit.description}</small>}
                            </td>
                            <td className="text-center">{produit.pivot?.quantite || 1}</td>
                            <td className="text-end">{formatCurrency(produit.pivot?.prix_unitaire || produit.prix || 0)}</td>
                            <td className="text-end fw-medium">
                              {formatCurrency((produit.pivot?.quantite || 1) * (produit.pivot?.prix_unitaire || produit.prix || 0))}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                </div>
              )}

              {/* Order Totals */}
              <div className="order-totals mb-3">
                <div className="bg-light p-3 rounded">
                  <Row>
                    <Col md={6}>
                      {/* Left side - can add notes or additional info */}
                      {selectedInvoice.order?.notes && (
                        <div>
                          <strong className="text-muted small">Notes:</strong>
                          <div className="small">{selectedInvoice.order.notes}</div>
                        </div>
                      )}
                    </Col>
                    <Col md={6}>
                      <div className="text-end">
                        {selectedInvoice.order?.subtotal && (
                          <div className="d-flex justify-content-between mb-1">
                            <span className="text-muted">Sous-total:</span>
                            <span>{formatCurrency(selectedInvoice.order.subtotal)}</span>
                          </div>
                        )}
                        {selectedInvoice.order?.shipping_cost && parseFloat(selectedInvoice.order.shipping_cost) > 0 && (
                          <div className="d-flex justify-content-between mb-1">
                            <span className="text-muted">Frais de livraison:</span>
                            <span>{formatCurrency(selectedInvoice.order.shipping_cost)}</span>
                          </div>
                        )}
                        {selectedInvoice.order?.tax_amount && parseFloat(selectedInvoice.order.tax_amount) > 0 && (
                          <div className="d-flex justify-content-between mb-1">
                            <span className="text-muted">TVA:</span>
                            <span>{formatCurrency(selectedInvoice.order.tax_amount)}</span>
                          </div>
                        )}
                        {selectedInvoice.order?.remise_commande && parseFloat(selectedInvoice.order.remise_commande) > 0 && (
                          <div className="d-flex justify-content-between mb-1 text-success">
                            <span>Remise:</span>
                            <span>-{formatCurrency(selectedInvoice.order.remise_commande)}</span>
                          </div>
                        )}
                        <hr className="my-2" />
                        <div className="d-flex justify-content-between">
                          <strong className="text-primary">Montant Total:</strong>
                          <strong className="text-primary fs-5">
                            {formatCurrency(selectedInvoice.amount || selectedInvoice.order?.total_commande || 0)}
                          </strong>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Payment Information */}
              <div className="payment-info mb-3">
                <h6 className="text-primary mb-2 d-flex align-items-center">
                  <FaCreditCard className="me-2" />
                  Informations de Paiement
                </h6>
                <div className="bg-light p-3 rounded">
                  <Row className="align-items-center">
                    <Col md={8}>
                      <div className="d-flex flex-wrap gap-3">
                        <div>
                          <strong className="text-muted small">Méthode:</strong>
                          <div className="small">
                            <Badge bg="secondary" className="text-capitalize">
                              {/* Use ONLY methode_paiement field directly from commande API */}
                              {selectedInvoice.methode_paiement ? selectedInvoice.methode_paiement.replace('_', ' ') : 'Non spécifiée'}
                            </Badge>
                          </div>
                        </div>
                        {selectedInvoice.transaction_id && (
                          <div>
                            <strong className="text-muted small">Transaction ID:</strong>
                            <div className="small font-monospace">{selectedInvoice.transaction_id}</div>
                          </div>
                        )}
                        {selectedInvoice.processed_at && (
                          <div>
                            <strong className="text-muted small">Traité le:</strong>
                            <div className="small">{formatDate(selectedInvoice.processed_at)}</div>
                          </div>
                        )}
                      </div>
                    </Col>
                    <Col md={4} className="text-end">
                      <div className="text-success">
                        <div className="small text-muted">Montant Payé</div>
                        <h5 className="mb-0 text-success">
                          <FaMoneyBillWave className="me-1" />
                          {formatCurrency(selectedInvoice.amount)}
                        </h5>
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Gateway Response (if available) */}
              {selectedInvoice.gateway_response && (
                <div className="gateway-info mb-3">
                  <h6 className="text-primary mb-2 d-flex align-items-center">
                    <FaServer className="me-2" />
                    Réponse de la Passerelle
                  </h6>
                  <div className="bg-light p-3 rounded">
                    <pre className="mb-0 small text-muted" style={{ fontSize: '0.75rem', maxHeight: '150px', overflowY: 'auto' }}>
                      {typeof selectedInvoice.gateway_response === 'string'
                        ? selectedInvoice.gateway_response
                        : JSON.stringify(selectedInvoice.gateway_response, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="bg-light border-0 py-2">
          <div className="d-flex justify-content-between w-100">
            <Button variant="outline-secondary" size="sm" onClick={() => setShowInvoiceModal(false)}>
              <FaTimes className="me-1" />
              Fermer
            </Button>
            <Button variant="primary" size="sm" onClick={() => window.print()}>
              <FaPrint className="me-1" />
              Imprimer
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          /* Hide non-essential elements */
          .btn,
          .breadcrumb,
          .alert,
          .modal-header,
          .modal-footer,
          .navbar,
          .sidebar,
          .container-fluid > *:not(.modal),
          body > *:not(.modal) {
            display: none !important;
          }

          /* Show only the modal content */
          .modal,
          .modal-dialog,
          .modal-content,
          .modal-body {
            display: block !important;
            position: static !important;
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
            max-height: none !important;
            overflow: visible !important;
            background: white !important;
            border: none !important;
            box-shadow: none !important;
          }

          /* Invoice styling */
          .invoice-details {
            margin: 0 !important;
            padding: 20px !important;
            background: white !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
          }

          /* Headers and sections */
          .invoice-header {
            border-bottom: 2px solid #0d6efd !important;
            margin-bottom: 20px !important;
            padding-bottom: 15px !important;
          }

          .invoice-header h5 {
            font-size: 18px !important;
            margin-bottom: 10px !important;
          }

          h6 {
            font-size: 14px !important;
            margin-bottom: 10px !important;
            color: #0d6efd !important;
            border-bottom: 1px solid #dee2e6 !important;
            padding-bottom: 5px !important;
          }

          /* Background and borders */
          .bg-light {
            background: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            padding: 10px !important;
          }

          /* Tables */
          .table {
            font-size: 11px !important;
            margin-bottom: 15px !important;
          }

          .table th {
            background: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            padding: 8px !important;
            font-weight: bold !important;
          }

          .table td {
            border: 1px solid #dee2e6 !important;
            padding: 6px !important;
          }

          /* Colors and text */
          .text-primary {
            color: #0d6efd !important;
          }

          .text-success {
            color: #198754 !important;
          }

          .text-muted {
            color: #6c757d !important;
          }

          /* Badges */
          .badge {
            border: 1px solid #000 !important;
            padding: 2px 6px !important;
            font-size: 10px !important;
          }

          /* Totals section */
          .order-totals {
            margin-top: 20px !important;
            border-top: 2px solid #0d6efd !important;
            padding-top: 15px !important;
          }

          /* Company info */
          .invoice-header .text-primary {
            font-weight: bold !important;
            font-size: 20px !important;
          }

          /* Page breaks */
          .payment-info,
          .gateway-info {
            page-break-inside: avoid !important;
          }

          /* Hide gateway response for cleaner print */
          .gateway-info {
            display: none !important;
          }
        }
      `}</style>
    </Container>
  );
};

export default Invoices;
